import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import { User } from '../models/user';

@Injectable({ providedIn: 'root' })
export class DbService {
  private db: PouchDB.Database;

  constructor() {
    // Create / open a local database (IndexedDB)
    this.db = new PouchDB('users_db');
    // Optional: log basic info
    this.db.info().then(info => {
      console.log('🚀 PouchDB ready:', info);
      this.logAllData();
    });
  }

  // Debug method to show all data in the database
  async logAllData() {
    try {
      const result = await this.db.allDocs({ include_docs: true });
      console.log('📚 All documents in PouchDB:', result);
      console.log('📊 Total documents:', result.total_rows);
      result.rows.forEach((row, index) => {
        console.log(`📄 Document ${index + 1}:`, row.doc);
      });
    } catch (error) {
      console.error('❌ Error fetching all data:', error);
    }
  }

  async addUser(user: Omit<User, '_id' | '_rev'>): Promise<PouchDB.Core.Response> {
    const doc: User = {
      ...user,
      type: 'user',
      createdAt: Date.now(),
    };
    // Use email as part of _id to avoid duplicates (demo choice)
    doc._id = `user:${doc.email}`;

    console.log('🔄 Attempting to save user to PouchDB:', doc);

    try {
      const result = await this.db.put(doc);
      console.log('✅ User saved successfully to PouchDB:', result);
      console.log('📊 Saved user data:', doc);
      return result;
    } catch (err: any) {
      // If id exists, append a random suffix (demo behavior)
      if (err.status === 409) {
        console.log('⚠️ User ID conflict, creating new ID...');
        doc._id = `user:${doc.email}:${Math.random().toString(36).slice(2, 8)}`;
        const result = await this.db.put(doc);
        console.log('✅ User saved with new ID:', result);
        console.log('📊 Saved user data:', doc);
        return result;
      }
      console.error('❌ Error saving user to PouchDB:', err);
      throw err;
    }
  }

  async getAllUsers(): Promise<User[]> {
    console.log('🔍 Fetching all users from PouchDB...');
    const res = await this.db.allDocs<User>({ include_docs: true });
    const users = res.rows
      .map(r => r.doc!)
      .filter(d => d && d.type === 'user')
      .sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));

    console.log('📋 Retrieved users from PouchDB:', users);
    console.log(`📊 Total users found: ${users.length}`);

    return users;
  }

  async deleteUser(id: string, rev: string): Promise<PouchDB.Core.Response> {
    console.log('🗑️ Deleting user from PouchDB:', { id, rev });
    const result = await this.db.remove(id, rev);
    console.log('✅ User deleted successfully:', result);
    return result;
  }

  // Optional: listen to live changes
  subscribeToChanges(onChange: () => void) {
    console.log('👂 Setting up PouchDB change listener...');
    return this.db
      .changes({ since: 'now', live: true, include_docs: false })
      .on('change', (change) => {
        console.log('🔄 PouchDB change detected:', change);
        onChange();
      })
      .on('error', (e) => console.error('❌ PouchDB changes error:', e));
  }
}
