import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import { User } from '../models/user';

@Injectable({ providedIn: 'root' })
export class DbService {
  private db: PouchDB.Database;

  constructor() {
    // Create / open a local database (IndexedDB)
    this.db = new PouchDB('users_db');
    // Optional: log basic info
    this.db.info().then(info => console.log('PouchDB ready:', info));
  }

  async addUser(user: Omit<User, '_id' | '_rev'>): Promise<PouchDB.Core.Response> {
    const doc: User = {
      ...user,
      type: 'user',
      createdAt: Date.now(),
    };
    // Use email as part of _id to avoid duplicates (demo choice)
    doc._id = `user:${doc.email}`;
    try {
      return await this.db.put(doc);
    } catch (err: any) {
      // If id exists, append a random suffix (demo behavior)
      if (err.status === 409) {
        doc._id = `user:${doc.email}:${Math.random().toString(36).slice(2, 8)}`;
        return await this.db.put(doc);
      }
      throw err;
    }
  }

  async getAllUsers(): Promise<User[]> {
    const res = await this.db.allDocs<User>({ include_docs: true });
    return res.rows
      .map(r => r.doc!)
      .filter(d => d && d.type === 'user')
      .sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));
  }

  async deleteUser(id: string, rev: string): Promise<PouchDB.Core.Response> {
    return this.db.remove(id, rev);
  }

  // Optional: listen to live changes
  subscribeToChanges(onChange: () => void) {
    return this.db
      .changes({ since: 'now', live: true, include_docs: false })
      .on('change', onChange)
      .on('error', (e) => console.error('Pouch changes error', e));
  }
}
