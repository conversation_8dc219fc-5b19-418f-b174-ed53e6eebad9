import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent
} from '@ionic/angular/standalone';
import { DbService } from '../../services/db.service';
import { User } from '../../models/user';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.page.html',
  styleUrls: ['./registration.page.scss'],
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent
  ]
})
export class RegistrationPage implements OnInit, OnDestroy {
  // Simple form model (template-driven)
  model = {
    name: '',
    email: '',
    password: '',
    age: null as number | null,
  };

  users: User[] = [];
  private changesCancel?: { cancel: () => void };

  loading = false;
  message = '';

  constructor(private db: DbService) {}

  async ngOnInit() {
    console.log('🎯 Registration page initialized');
    await this.loadUsers();
    // Refresh list when DB changes
    this.changesCancel = this.db.subscribeToChanges(async () => {
      console.log('🔄 Database change detected, reloading users...');
      await this.loadUsers();
    });
  }

  ngOnDestroy(): void {
    this.changesCancel?.cancel?.();
  }

  async onSubmit(event: Event) {
    event.preventDefault();
    this.message = '';

    console.log('📝 Form submitted with data:', this.model);

    if (!this.model.name || !this.model.email || !this.model.password) {
      this.message = 'Please fill name, email, and password.';
      console.log('⚠️ Form validation failed - missing required fields');
      return;
    }

    this.loading = true;
    console.log('⏳ Starting user registration process...');

    try {
      const userData = {
        name: this.model.name.trim(),
        email: this.model.email.trim().toLowerCase(),
        password: this.model.password, // demo only
        age: this.model.age ?? undefined,
        type: 'user',        // will be overridden in service anyway
        createdAt: Date.now()
      };

      console.log('📤 Sending user data to database:', userData);

      await this.db.addUser(userData as any);
      this.message = 'Saved!';
      console.log('✅ User registration completed successfully!');

      // reset form
      this.model = { name: '', email: '', password: '', age: null };
      console.log('🔄 Form reset, reloading user list...');
      await this.loadUsers();
    } catch (e: any) {
      console.error('❌ Error during user registration:', e);
      this.message = e?.message || 'Error saving data.';
    } finally {
      this.loading = false;
      console.log('🏁 Registration process completed');
    }
  }

  async loadUsers() {
    console.log('📋 Loading users from database...');
    this.users = await this.db.getAllUsers();
    console.log('✅ Users loaded in component:', this.users);
  }

  async remove(u: User) {
    if (!u._id || !u._rev) {
      console.log('⚠️ Cannot delete user - missing ID or revision:', u);
      return;
    }
    console.log('🗑️ Deleting user:', u);
    await this.db.deleteUser(u._id, u._rev);
    console.log('✅ User deleted, reloading list...');
  }
}
