import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent
} from '@ionic/angular/standalone';
import { DbService } from '../../services/db.service';
import { User } from '../../models/user';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.page.html',
  styleUrls: ['./registration.page.scss'],
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent
  ]
})
export class RegistrationPage implements OnInit, OnDestroy {
  // Simple form model (template-driven)
  model = {
    name: '',
    email: '',
    password: '',
    age: null as number | null,
  };

  users: User[] = [];
  private changesCancel?: { cancel: () => void };

  loading = false;
  message = '';

  constructor(private db: DbService) {}

  async ngOnInit() {
    await this.loadUsers();
    // Refresh list when DB changes
    this.changesCancel = this.db.subscribeToChanges(async () => {
      await this.loadUsers();
    });
  }

  ngOnDestroy(): void {
    this.changesCancel?.cancel?.();
  }

  async onSubmit(event: Event) {
    event.preventDefault();
    this.message = '';
    if (!this.model.name || !this.model.email || !this.model.password) {
      this.message = 'Please fill name, email, and password.';
      return;
    }
    this.loading = true;
    try {
      await this.db.addUser({
        name: this.model.name.trim(),
        email: this.model.email.trim().toLowerCase(),
        password: this.model.password, // demo only
        age: this.model.age ?? undefined,
        type: 'user',        // will be overridden in service anyway
        createdAt: Date.now()
      } as any);
      this.message = 'Saved!';
      // reset form
      this.model = { name: '', email: '', password: '', age: null };
      await this.loadUsers();
    } catch (e: any) {
      console.error(e);
      this.message = e?.message || 'Error saving data.';
    } finally {
      this.loading = false;
    }
  }

  async loadUsers() {
    this.users = await this.db.getAllUsers();
  }

  async remove(u: User) {
    if (!u._id || !u._rev) return;
    await this.db.deleteUser(u._id, u._rev);
  }
}
