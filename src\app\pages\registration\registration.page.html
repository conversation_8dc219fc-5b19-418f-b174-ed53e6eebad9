<ion-header>
  <ion-toolbar>
    <ion-title>Registration (PouchDB Demo)</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- Simple HTML form (no Ionic form components) -->
  <form (submit)="onSubmit($event.target); $event.preventDefault()">
    <div class="field">
      <label for="name">Name</label>
      <input id="name" name="name" [(ngModel)]="model.name" type="text" placeholder="Your name" />
    </div>

    <div class="field">
      <label for="email">Email</label>
      <input id="email" name="email" [(ngModel)]="model.email" type="email" placeholder="<EMAIL>" />
    </div>

    <div class="field">
      <label for="password">Password</label>
      <input id="password" name="password" [(ngModel)]="model.password" type="password" placeholder="******" />
    </div>

    <div class="field">
      <label for="age">Age</label>
      <input id="age" name="age" [(ngModel)]="model.age" type="number" min="0" />
    </div>

    <button type="submit" [disabled]="loading">Register</button>
  </form>

  <p *ngIf="message">{{ message }}</p>

  <hr />

  <h3>Saved Users</h3>
  <div *ngIf="users.length === 0">No users yet.</div>

  <ul>
    <li *ngFor="let u of users">
      <strong>{{ u.name }}</strong> — {{ u.email }}
      <span *ngIf="u.age"> ({{ u.age }})</span>
      <button (click)="remove(u)">Delete</button>
    </li>
  </ul>

</ion-content>
